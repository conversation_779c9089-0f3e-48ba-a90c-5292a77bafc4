# SisaRasa Railway Deployment Guide

## 🚀 Deployment Readiness Status: ✅ READY

### Pre-Deployment Fixes Completed:
- ✅ Fixed critical app initialization issues
- ✅ Registered all blueprints (routes, auth, analytics)
- ✅ Added proper Flask app factory with JWT & MongoDB
- ✅ Fixed ML model loading with error handling
- ✅ Updated Dockerfile with Gunicorn for production
- ✅ Configured static files path for Railway

## Railway Deployment Steps

### Step 1: Create Railway Project
1. Go to [railway.app](https://railway.app) and login
2. Click "New Project" → "Deploy from GitHub repo"
3. Select your SisaRasa repository
4. Railway will auto-detect Dockerfile and build

### Step 2: Configure Environment Variables
Add these in Railway Variables tab:

```bash
# MongoDB (CRITICAL)
MONGO_URI=mongodb+srv://farahfiqh:<EMAIL>/sisarasa?retryWrites=true&w=majority&appName=Cluster0

# JWT Security (CRITICAL)  
JWT_SECRET_KEY=sisarasa-recipe-recommendation-system-secret-key-change-in-production-2024
JWT_ACCESS_TOKEN_EXPIRES=86400

# Railway Config
RAILWAY_ENVIRONMENT=production
DEBUG=False
HOST=0.0.0.0
```

### Step 3: Fast Deployment & Health Check
1. Railway builds using fast-startup mode (no ML loading)
2. Health checks pass within 30 seconds
3. App starts in minimal mode for Railway verification

### Step 4: Initialize Full System
After deployment is healthy, run:
```bash
python initialize_system.py https://your-app.railway.app
```

This will:
- Load 3,000+ recipes and ML models
- Register all API endpoints
- Enable full functionality

## System Specifications
- **Application Type:** Complete Web Application (not just API)
- **Recipes Available:** 3,000+ recipes (Railway optimized)
- **Ingredients:** 36,000+ unique ingredients
- **ML System:** Hybrid KNN + Content-based + Collaborative filtering
- **Memory Usage:** ~300MB RAM (Railway optimized)
- **Startup Time:** 30 seconds (fast web interface, ML loads after)

## Web Pages Available
- 🏠 **Welcome Page** (`/welcome`) - Landing page with system info
- 🔐 **Login Page** (`/login`) - User authentication
- 📝 **Signup Page** (`/signup`) - User registration
- 🍳 **Dashboard** (`/dashboard`) - Recipe recommendation interface
- 👤 **Profile** (`/profile`) - User account management
- 💾 **Saved Recipes** (`/save-recipe`) - User's favorite recipes
- 👥 **Community** (`/community-recipes`) - Recipe sharing and reviews
- 📤 **Share Recipe** (`/share-recipe`) - Submit new recipes
- 🔍 **Search Results** (`/search-results`) - Recipe recommendations display

## Core Features Ready for Testing
- ✅ Complete web interface (accessible via browser)
- ✅ User authentication (signup/login pages)
- ✅ Recipe recommendations with web interface
- ✅ Community features (sharing, reviews, ratings)
- ✅ Profile management and saved recipes
- ✅ Responsive design for desktop and mobile
- ✅ All pages with fallback HTML if templates fail

## MongoDB Options
**Option A:** Use existing MongoDB Atlas (recommended)
**Option B:** Add Railway MongoDB plugin

## Production Configuration
- **Web Server:** Gunicorn with 2 workers
- **Timeout:** 300 seconds for ML operations
- **Health Checks:** Automatic Railway monitoring
- **Security:** Non-root user, secure JWT tokens

## Post-Deployment Testing Checklist
- [ ] User registration works
- [ ] Login authentication successful  
- [ ] Recipe search returns results
- [ ] Community features accessible
- [ ] Profile management functional
- [ ] All pages load with proper styling

Ready for Railway deployment! 🚀
